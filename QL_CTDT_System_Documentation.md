# Tài liệu Hệ thống QL_CTDT (Quản lý Chương trình Đào tạo)

## 📋 Tổng quan Hệ thống

**QL_CTDT** là một hệ thống quản lý chương trình đào tạo hiện đại, đ<PERSON><PERSON><PERSON> xây dựng với kiến trúc full-stack, hỗ trợ thi trực tuyến realtime và quản lý học tập toàn diện.

### 🎯 Mục đích chính
- Quản lý chương trình đào tạo (Programs, PO, PLO)
- Quản lý môn học và khóa học (Courses, Subjects, Chapters)
- <PERSON>ệ thống thi trực tuyến realtime với Socket.IO
- Quản lý người dùng và phân quyền (Admin, Teacher, Student)
- <PERSON> k<PERSON> qu<PERSON> học tập và analytics
- Quản lý câu hỏi và bài kiểm tra

## 🏗️ Kiến trúc Hệ thống

### Sơ đồ Kiến trúc Tổng quan

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        MOBILE[Mobile Browser]
    end

    subgraph "Load Balancer & Reverse Proxy"
        NGINX[Nginx<br/>- SSL Termination<br/>- Load Balancing<br/>- Static Files]
    end

    subgraph "Frontend Layer"
        NEXTJS[Next.js Frontend<br/>- React 19<br/>- TypeScript<br/>- Tailwind CSS<br/>- Socket.IO Client]
    end

    subgraph "Backend Layer"
        API[Express.js API Server<br/>- RESTful APIs<br/>- JWT Authentication<br/>- Role-based Authorization]
        SOCKET[Socket.IO Server<br/>- Real-time Quiz<br/>- Live Updates<br/>- WebSocket Management]
        MIDDLEWARE[Middleware Layer<br/>- Auth Middleware<br/>- Quiz Session<br/>- File Upload]
    end

    subgraph "Business Logic Layer"
        CONTROLLERS[Controllers<br/>- User Controller<br/>- Quiz Controller<br/>- Course Controller<br/>- Program Controller]
        SERVICES[Services<br/>- Quiz Realtime Service<br/>- Auth Service<br/>- File Service]
    end

    subgraph "Data Access Layer"
        ORM[Sequelize ORM<br/>- Model Definitions<br/>- Relationships<br/>- Migrations]
    end

    subgraph "Database Layer"
        POSTGRES[(PostgreSQL<br/>- User Data<br/>- Course Data<br/>- Quiz Data<br/>- Results)]
        REDIS[(Redis Cache<br/>- Session Storage<br/>- Quiz State<br/>- Real-time Data)]
    end

    subgraph "External Services"
        FIREBASE[Firebase<br/>- Real-time Database<br/>- Push Notifications]
        STORAGE[File Storage<br/>- Question Images<br/>- Course Materials]
    end

    subgraph "Infrastructure"
        DOCKER[Docker Containers<br/>- Frontend Container<br/>- Backend Container<br/>- Database Containers]
    end

    %% Connections
    WEB --> NGINX
    MOBILE --> NGINX
    NGINX --> NEXTJS
    NGINX --> API
    NGINX --> SOCKET

    NEXTJS --> API
    NEXTJS --> SOCKET

    API --> MIDDLEWARE
    SOCKET --> MIDDLEWARE
    MIDDLEWARE --> CONTROLLERS
    CONTROLLERS --> SERVICES
    SERVICES --> ORM

    ORM --> POSTGRES
    SERVICES --> REDIS
    SERVICES --> FIREBASE
    CONTROLLERS --> STORAGE

    DOCKER -.-> NEXTJS
    DOCKER -.-> API
    DOCKER -.-> SOCKET
    DOCKER -.-> POSTGRES
    DOCKER -.-> REDIS

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef database fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef infrastructure fill:#fce4ec

    class NEXTJS frontend
    class API,SOCKET,MIDDLEWARE,CONTROLLERS,SERVICES,ORM backend
    class POSTGRES,REDIS database
    class FIREBASE,STORAGE external
    class DOCKER infrastructure
```

### Frontend Layer
- **Framework**: Next.js 15 với React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI, Lucide React
- **State Management**: React Hooks, Custom Hooks
- **Real-time**: Socket.IO Client
- **Authentication**: JWT với role-based access control

### Backend Layer
- **Runtime**: Node.js
- **Framework**: Express.js
- **Real-time**: Socket.IO Server
- **ORM**: Sequelize
- **Authentication**: JWT, bcrypt
- **File Upload**: Multer
- **Validation**: Custom middleware

### Database Layer
- **Primary Database**: PostgreSQL
  - User data, Course data, Quiz data, Results
- **Cache Database**: Redis
  - Session storage, Quiz state, Real-time data
- **External Database**: Firebase
  - Real-time database, Push notifications

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx
- **SSL**: Let's Encrypt certificates
- **File Storage**: Local storage với upload handling

## 👥 Vai trò Người dùng

### Sơ đồ Use Case UML - Phần 1: Quản lý Hệ thống & Giảng dạy

```mermaid
graph TB
    %% Actors
    Admin((Admin))
    Teacher((Giảng viên))

    %% System boundary
    subgraph "Hệ thống QL_CTDT - Quản lý & Giảng dạy"
        %% Admin Use Cases
        UC1((Quản lý<br/>người dùng))
        UC2((Phân quyền<br/>vai trò))
        UC3((Quản lý chương<br/>trình đào tạo))
        UC4((Cấu hình<br/>hệ thống))
        UC5((Xem báo cáo<br/>tổng quan))

        %% Teacher Use Cases
        UC6((Quản lý<br/>môn học))
        UC7((Tạo/Chỉnh sửa<br/>câu hỏi))
        UC8((Tạo bài<br/>kiểm tra))
        UC9((Quản lý quiz<br/>realtime))
        UC10((Xem kết quả<br/>học sinh))
        UC11((Xuất<br/>báo cáo))
        UC12((Upload<br/>tài liệu))

        %% Common Use Cases
        UC18((Đăng nhập))
        UC19((Đăng xuất))
        UC20((Quản lý<br/>profile))
    end

    %% Admin relationships
    Admin --- UC1
    Admin --- UC2
    Admin --- UC3
    Admin --- UC4
    Admin --- UC5
    Admin --- UC18
    Admin --- UC19
    Admin --- UC20

    %% Teacher relationships
    Teacher --- UC6
    Teacher --- UC7
    Teacher --- UC8
    Teacher --- UC9
    Teacher --- UC10
    Teacher --- UC11
    Teacher --- UC12
    Teacher --- UC18
    Teacher --- UC19
    Teacher --- UC20

    %% Include relationships
    UC8 -.->|include| UC7
    UC11 -.->|extend| UC10
    UC5 -.->|extend| UC1

    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px

    class Admin,Teacher actor
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC18,UC19,UC20 usecase
```

### Sơ đồ Use Case UML - Phần 2: Học tập & Quiz Realtime

```mermaid
graph TB
    %% Actors
    Teacher((Giảng viên))
    Student((Sinh viên))

    %% System boundary
    subgraph "Hệ thống QL_CTDT - Học tập & Quiz"
        %% Student Use Cases
        UC13((Đăng ký<br/>môn học))
        UC14((Tham gia quiz<br/>realtime))
        UC15((Xem kết quả<br/>bài thi))
        UC16((Theo dõi tiến<br/>độ học tập))
        UC17((Tải tài liệu<br/>môn học))

        %% Common Use Cases
        UC18((Đăng nhập))
        UC19((Đăng xuất))
        UC20((Quản lý<br/>profile))

        %% Quiz Realtime Use Cases
        UC21((Tạo phòng<br/>quiz))
        UC22((Tham gia<br/>phòng chờ))
        UC23((Bắt đầu<br/>quiz))
        UC24((Trả lời<br/>câu hỏi))
        UC25((Xem bảng<br/>xếp hạng))
        UC26((Kết thúc<br/>quiz))
    end

    %% Teacher relationships (Quiz related)
    Teacher --- UC21
    Teacher --- UC23
    Teacher --- UC25
    Teacher --- UC26
    Teacher --- UC18
    Teacher --- UC19
    Teacher --- UC20

    %% Student relationships
    Student --- UC13
    Student --- UC14
    Student --- UC15
    Student --- UC16
    Student --- UC17
    Student --- UC18
    Student --- UC19
    Student --- UC20
    Student --- UC22
    Student --- UC24
    Student --- UC25

    %% Include relationships
    UC14 -.->|include| UC22
    UC14 -.->|include| UC24

    %% Extend relationships
    UC25 -.->|extend| UC14
    UC25 -.->|extend| UC23

    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px

    class Teacher,Student actor
    class UC13,UC14,UC15,UC16,UC17,UC18,UC19,UC20,UC21,UC22,UC23,UC24,UC25,UC26 usecase
```

### 🔧 Admin
- Quản lý người dùng (CRUD operations)
- Phân quyền vai trò
- Quản lý chương trình đào tạo
- Cấu hình hệ thống
- Xem báo cáo tổng quan

### 👨‍🏫 Giảng viên (Teacher)
- Quản lý môn học
- Tạo/Chỉnh sửa câu hỏi
- Tạo bài kiểm tra
- Quản lý quiz realtime
- Xem kết quả học sinh
- Xuất báo cáo
- Upload tài liệu

### 👨‍🎓 Sinh viên (Student)
- Đăng ký môn học
- Tham gia quiz realtime
- Xem kết quả bài thi
- Theo dõi tiến độ học tập
- Tải tài liệu môn học

## 🎮 Tính năng Quiz Realtime

### Đặc điểm nổi bật
- **Real-time synchronization**: Đồng bộ thời gian thực với Socket.IO
- **Live leaderboard**: Bảng xếp hạng cập nhật trực tiếp
- **Multi-room support**: Hỗ trợ nhiều phòng quiz đồng thời
- **Auto-progression**: Tự động chuyển câu hỏi
- **Session management**: Quản lý phiên làm bài với Redis
- **Analytics tracking**: Theo dõi chi tiết quá trình làm bài

### Sơ đồ Luồng Quiz Realtime

```mermaid
sequenceDiagram
    participant T as 👨‍🏫 Giảng viên
    participant S as 👨‍🎓 Sinh viên
    participant FE as Frontend
    participant BE as Backend API
    participant WS as Socket.IO Server
    participant R as Redis Cache
    participant FB as Firebase
    participant DB as PostgreSQL

    Note over T,DB: Giai đoạn 1: Tạo và Chuẩn bị Quiz

    T->>FE: Tạo quiz mới
    FE->>BE: POST /api/quizzes
    BE->>DB: Lưu thông tin quiz
    DB-->>BE: Quiz ID
    BE-->>FE: Quiz được tạo
    FE-->>T: Hiển thị quiz dashboard

    T->>FE: Bắt đầu quiz
    FE->>BE: POST /api/quizzes/{id}/start
    BE->>R: Tạo quiz session
    BE->>FB: Tạo quiz room
    BE->>WS: Khởi tạo Socket rooms
    WS-->>BE: Rooms created
    BE-->>FE: Quiz started

    Note over T,DB: Giai đoạn 2: Sinh viên Tham gia

    S->>FE: Nhập mã PIN quiz
    FE->>BE: POST /api/quizzes/join
    BE->>R: Kiểm tra quiz session
    R-->>BE: Session valid
    BE->>FB: Thêm participant
    BE->>WS: Join quiz rooms
    WS->>WS: Emit 'newParticipant'
    WS-->>FE: Participant joined
    FE-->>S: Vào phòng chờ

    Note over T,DB: Giai đoạn 3: Bắt đầu Quiz Realtime

    T->>FE: Start quiz
    FE->>BE: POST /api/quizzes/{id}/start-auto
    BE->>R: Lấy danh sách câu hỏi
    BE->>WS: Emit 'quizStarted'
    WS-->>FE: Quiz bắt đầu
    FE-->>S: Chuyển đến trang quiz
    FE-->>T: Chuyển đến trang quiz

    loop Mỗi câu hỏi
        BE->>WS: Emit 'newQuestion'
        WS-->>FE: Nhận câu hỏi mới
        FE-->>S: Hiển thị câu hỏi
        FE-->>T: Hiển thị câu hỏi

        Note over S,R: Sinh viên trả lời
        S->>FE: Chọn đáp án
        FE->>BE: POST /api/quizzes/realtime/answer
        BE->>R: Lưu câu trả lời
        BE->>DB: Lưu kết quả
        BE->>FB: Cập nhật điểm số

        Note over BE,WS: Tính toán và cập nhật
        BE->>BE: Tính điểm
        BE->>R: Cập nhật leaderboard
        BE->>WS: Emit 'showAnswerResult'
        WS-->>FE: Hiển thị kết quả
        FE-->>S: Đáp án đúng/sai

        Note over BE,WS: Chuyển câu tiếp theo
        BE->>WS: Emit 'nextQuestion' (sau 3s)
        WS-->>FE: Câu hỏi tiếp theo
    end

    Note over T,DB: Giai đoạn 4: Kết thúc và Bảng xếp hạng

    BE->>R: Tính toán bảng xếp hạng cuối
    BE->>WS: Emit 'quizCompleted'
    WS-->>FE: Quiz hoàn thành
    FE-->>S: Hiển thị kết quả cá nhân
    FE-->>T: Hiển thị bảng xếp hạng

    T->>FE: Xem báo cáo chi tiết
    FE->>BE: GET /api/quiz-results/{id}
    BE->>DB: Truy vấn kết quả
    DB-->>BE: Dữ liệu kết quả
    BE-->>FE: Báo cáo chi tiết
    FE-->>T: Dashboard kết quả

    Note over T,DB: Giai đoạn 5: Cleanup

    BE->>R: Xóa quiz session
    BE->>FB: Cleanup quiz room
    BE->>WS: Disconnect rooms
    WS-->>BE: Rooms cleaned

    Note over T,DB: Các tính năng bổ sung

    rect rgb(255, 248, 220)
        Note over FE,R: Realtime Features
        WS->>FE: Live participant count
        WS->>FE: Live leaderboard updates
        WS->>FE: Question timer sync
        WS->>FE: Connection status
    end

    rect rgb(240, 248, 255)
        Note over BE,DB: Analytics & Tracking
        BE->>DB: User question history
        BE->>DB: Quiz analytics
        BE->>DB: Learning path tracking
        BE->>R: Performance metrics
    end
```

### Luồng hoạt động
1. **Tạo Quiz**: Giảng viên tạo quiz và câu hỏi
2. **Khởi tạo Session**: Tạo phòng chờ với mã PIN
3. **Tham gia**: Sinh viên nhập mã PIN để tham gia
4. **Bắt đầu**: Giảng viên khởi động quiz
5. **Làm bài**: Sinh viên trả lời câu hỏi realtime
6. **Kết quả**: Hiển thị đáp án và điểm số ngay lập tức
7. **Bảng xếp hạng**: Cập nhật thứ hạng theo thời gian thực
8. **Báo cáo**: Xuất kết quả chi tiết

## 🗄️ Cấu trúc Cơ sở Dữ liệu

### Sơ đồ Entity Relationship Diagram (ERD)

```mermaid
erDiagram
    %% Core Entities
    Programs {
        int program_id PK
        string name
        text description
    }

    Roles {
        int role_id PK
        string name
    }

    Users {
        int user_id PK
        string name
        string email UK
        string password
        int role_id FK
    }

    %% Program Structure
    POs {
        int po_id PK
        string name
        text description
        int program_id FK
    }

    PLOs {
        int plo_id PK
        text description
        int program_id FK
    }

    POsPLOs {
        int po_id FK
        int plo_id FK
    }

    %% Course Structure
    Courses {
        int course_id PK
        string name
        text description
        int program_id FK
        int user_id FK
    }

    StudentCourses {
        int user_id FK
        int course_id FK
    }

    TypeSubjects {
        int type_id PK
        text description
    }

    TypeOfKnowledges {
        int noidung_id PK
        text description
    }

    Groups {
        int group_id PK
        text description
    }

    Subjects {
        int subject_id PK
        string name
        text description
        int course_id FK
        int type_id FK
        int noidung_id FK
        int plo_id FK
    }

    Chapters {
        int chapter_id PK
        string name
        text description
        int subject_id FK
    }

    LOs {
        int lo_id PK
        string name
        text description
    }

    ChapterLOs {
        int chapter_id FK
        int lo_id FK
    }

    %% Quiz Structure
    QuestionTypes {
        int type_id PK
        string name
    }

    Levels {
        int level_id PK
        string name
    }

    Questions {
        int question_id PK
        text content
        text explanation
        int lo_id FK
        int type_id FK
        int level_id FK
        int group_id FK
    }

    Answers {
        int answer_id PK
        text content
        boolean is_correct
        int question_id FK
    }

    Quizzes {
        int quiz_id PK
        string name
        text description
        int subject_id FK
        int time_limit
        boolean is_active
    }

    QuizQuestions {
        int quiz_id FK
        int question_id FK
        int order_index
    }

    %% Results
    QuizResults {
        int result_id PK
        int user_id FK
        int quiz_id FK
        int score
        datetime completed_at
    }

    CourseResults {
        int result_id PK
        int user_id FK
        int course_id FK
        float final_score
        string grade
    }

    %% Prerequisites
    TienQuyets {
        int subject_id FK
        int prerequisite_subject_id FK
    }

    %% Analytics & Tracking
    QuizAnalytics {
        int analytics_id PK
        int quiz_id FK
        int total_participants
        float average_score
        datetime created_at
    }

    UserQuizTracking {
        int tracking_id PK
        int user_id FK
        int quiz_id FK
        int current_question_index
        datetime started_at
        string status
    }

    UserQuestionHistory {
        int history_id PK
        int user_id FK
        int question_id FK
        int selected_answer_id FK
        boolean is_correct
        datetime answered_at
    }

    UserLearningPath {
        int path_id PK
        int user_id FK
        int lo_id FK
        string mastery_level
        datetime last_practiced
    }

    %% Relationships
    Programs ||--o{ POs : has
    Programs ||--o{ PLOs : has
    Programs ||--o{ Courses : contains

    POs ||--o{ POsPLOs : links
    PLOs ||--o{ POsPLOs : links

    Roles ||--o{ Users : assigns

    Users ||--o{ Courses : teaches
    Users ||--o{ StudentCourses : enrolls
    Users ||--o{ QuizResults : takes
    Users ||--o{ CourseResults : achieves
    Users ||--o{ UserQuizTracking : tracks
    Users ||--o{ UserQuestionHistory : records
    Users ||--o{ UserLearningPath : follows

    Courses ||--o{ StudentCourses : includes
    Courses ||--o{ Subjects : contains
    Courses ||--o{ CourseResults : evaluates

    TypeSubjects ||--o{ Subjects : categorizes
    TypeOfKnowledges ||--o{ Subjects : classifies
    PLOs ||--o{ Subjects : maps

    Subjects ||--o{ Chapters : organizes
    Subjects ||--o{ Quizzes : tests
    Subjects ||--o{ TienQuyets : requires
    Subjects ||--o{ TienQuyets : prerequisite

    Chapters ||--o{ ChapterLOs : contains
    LOs ||--o{ ChapterLOs : belongs
    LOs ||--o{ Questions : covers
    LOs ||--o{ UserLearningPath : guides

    QuestionTypes ||--o{ Questions : types
    Levels ||--o{ Questions : difficulty
    Groups ||--o{ Questions : groups

    Questions ||--o{ Answers : has
    Questions ||--o{ QuizQuestions : includes
    Questions ||--o{ UserQuestionHistory : answered

    Quizzes ||--o{ QuizQuestions : contains
    Quizzes ||--o{ QuizResults : generates
    Quizzes ||--o{ QuizAnalytics : analyzes
    Quizzes ||--o{ UserQuizTracking : monitors

    Answers ||--o{ UserQuestionHistory : selected
```

### Core Entities
- **Programs**: Chương trình đào tạo
- **PO/PLO**: Program Outcomes / Program Learning Outcomes
- **Courses**: Khóa học
- **Subjects**: Môn học
- **Chapters**: Chương học
- **LO**: Learning Outcomes

### Quiz System
- **Questions**: Câu hỏi với phân loại theo LO, độ khó
- **Answers**: Đáp án cho từng câu hỏi
- **Quizzes**: Bài kiểm tra
- **QuizResults**: Kết quả làm bài

### User Management
- **Users**: Thông tin người dùng
- **Roles**: Vai trò (Admin, Teacher, Student)
- **StudentCourses**: Đăng ký môn học

### Analytics & Tracking
- **UserQuizTracking**: Theo dõi quá trình làm bài
- **UserQuestionHistory**: Lịch sử trả lời câu hỏi
- **QuizAnalytics**: Phân tích kết quả quiz
- **UserLearningPath**: Lộ trình học tập cá nhân

## 🚀 Deployment

### Docker Compose Services
```yaml
services:
  - postgres: PostgreSQL database
  - redis: Redis cache
  - backend: Node.js API server
  - frontend: Next.js application
  - nginx: Reverse proxy & SSL termination
```

### Network Configuration
- **Frontend**: Port 3000
- **Backend**: Port 8888
- **PostgreSQL**: Port 5433
- **Redis**: Port 6379
- **Nginx**: Port 80/443 (HTTP/HTTPS)

### SSL & Security
- Let's Encrypt SSL certificates
- HTTPS redirect
- HSTS headers
- JWT authentication
- Password hashing with bcrypt

## 📊 API Endpoints

Hệ thống QL_CTDT cung cấp một bộ API RESTful toàn diện với phân quyền chi tiết theo vai trò người dùng.

### 🔐 Phân quyền API
- **Public**: Không cần xác thực (chỉ xem dữ liệu cơ bản)
- **Admin**: Toàn quyền CRUD, quản lý hệ thống
- **Teacher**: Quản lý giảng dạy, xem báo cáo
- **Student**: Tham gia học tập, làm bài kiểm tra

---

### 👤 Authentication & User Management
**Base URL**: `/api/users`

#### Public Endpoints
```http
POST /api/users/login                    # Đăng nhập hệ thống
```

#### Admin Only Endpoints
```http
GET /api/users                          # Lấy danh sách tất cả người dùng
POST /api/users/createAdmin             # Tạo tài khoản Admin
POST /api/users/createTeacher           # Tạo tài khoản Giảng viên
DELETE /api/users/:id                   # Xóa người dùng
```

#### Admin & Teacher Endpoints
```http
POST /api/users/createStudent           # Tạo tài khoản Sinh viên
POST /api/users/importStudents          # Import danh sách sinh viên từ file
```

#### All Authenticated Users
```http
GET /api/users/:id                      # Lấy thông tin người dùng theo ID
PUT /api/users/:id                      # Cập nhật thông tin cá nhân
```

---

### 🎯 Program Management
**Base URL**: `/api/programs`

#### Public Endpoints
```http
GET /api/programs                       # Lấy danh sách chương trình đào tạo
GET /api/programs/:id                   # Lấy chi tiết chương trình
```

#### Admin Only Endpoints
```http
POST /api/programs                      # Tạo chương trình đào tạo mới
PUT /api/programs/:id                   # Cập nhật chương trình
DELETE /api/programs/:id                # Xóa chương trình
```

---

### 🎓 Program Outcomes (PO) Management
**Base URL**: `/api/pos`

#### Public Endpoints
```http
GET /api/pos                           # Lấy danh sách tất cả PO
GET /api/pos/:id                       # Lấy chi tiết PO
GET /api/pos/program/:program_id       # Lấy PO theo chương trình
```

#### Admin Only Endpoints
```http
POST /api/pos                          # Tạo PO mới
PUT /api/pos/:id                       # Cập nhật PO
DELETE /api/pos/:id                    # Xóa PO
POST /api/pos/bulk/create              # Tạo hàng loạt PO
PUT /api/pos/bulk/update               # Cập nhật hàng loạt PO
DELETE /api/pos/bulk/delete            # Xóa hàng loạt PO
```

#### Statistics Endpoints (Admin + Teacher)
```http
GET /api/pos/statistics/overview                    # Thống kê tổng quan PO
GET /api/pos/statistics/achievement/:program_id     # Phân tích đạt được PO
```

---

### 📚 Program Learning Outcomes (PLO) Management
**Base URL**: `/api/plos`

#### Public Endpoints
```http
GET /api/plos                          # Lấy danh sách tất cả PLO
GET /api/plos/:id                      # Lấy chi tiết PLO
GET /api/plos/program/:program_id      # Lấy PLO theo chương trình
```

#### Admin Only Endpoints
```http
POST /api/plos                         # Tạo PLO mới
PUT /api/plos/:id                      # Cập nhật PLO
DELETE /api/plos/:id                   # Xóa PLO
POST /api/plos/bulk/create             # Tạo hàng loạt PLO
PUT /api/plos/bulk/update              # Cập nhật hàng loạt PLO
DELETE /api/plos/bulk/delete           # Xóa hàng loạt PLO
```

#### Statistics Endpoints (Admin + Teacher)
```http
GET /api/plos/statistics/overview                   # Thống kê tổng quan PLO
GET /api/plos/statistics/achievement/:program_id    # Phân tích đạt được PLO
```

---

### 🔗 PO-PLO Relationships
**Base URL**: `/api/pos-plos`

#### Public Endpoints
```http
GET /api/pos-plos                      # Lấy tất cả mối quan hệ PO-PLO
GET /api/pos-plos/:po_id/:plo_id       # Lấy mối quan hệ cụ thể
```

#### Admin Only Endpoints
```http
POST /api/pos-plos                     # Tạo mối quan hệ PO-PLO
DELETE /api/pos-plos/:po_id/:plo_id    # Xóa mối quan hệ PO-PLO
```

---

### 📖 Course Management
**Base URL**: `/api/courses`

#### Public Endpoints
```http
GET /api/courses                       # Lấy danh sách khóa học
GET /api/courses/:id                   # Lấy chi tiết khóa học
```

#### Admin Only Endpoints
```http
POST /api/courses                      # Tạo khóa học mới
PUT /api/courses/:id                   # Cập nhật khóa học
DELETE /api/courses/:id                # Xóa khóa học
```

---

### 📝 Subject Management
**Base URL**: `/api/subjects`

#### Public Endpoints
```http
GET /api/subjects                      # Lấy danh sách môn học
GET /api/subjects/:id                  # Lấy chi tiết môn học
```

#### Admin Only Endpoints
```http
POST /api/subjects                     # Tạo môn học mới
PUT /api/subjects/:id                  # Cập nhật môn học
DELETE /api/subjects/:id               # Xóa môn học
```

---

### 📑 Chapter Management
**Base URL**: `/api/chapters`

#### Public Endpoints
```http
GET /api/chapters                      # Lấy danh sách chương học
GET /api/chapters/:id                  # Lấy chi tiết chương học
GET /api/chapters/subject/:subject_id  # Lấy chương theo môn học
```

#### Admin Only Endpoints
```http
POST /api/chapters                     # Tạo chương học mới
PUT /api/chapters/:id                  # Cập nhật chương học
DELETE /api/chapters/:id               # Xóa chương học
POST /api/chapters/bulk/create         # Tạo hàng loạt chương học
DELETE /api/chapters/bulk/delete       # Xóa hàng loạt chương học
```

#### Statistics Endpoints (Admin + Teacher)
```http
GET /api/chapters/statistics/overview  # Thống kê tổng quan chương học
```

---

### 🎯 Learning Outcomes (LO) Management
**Base URL**: `/api/los`

#### Public Endpoints
```http
GET /api/los                           # Lấy danh sách tất cả LO
GET /api/los/:id                       # Lấy chi tiết LO
GET /api/los/subject/:subjectId        # Lấy LO theo môn học
```

#### Admin Only Endpoints
```http
POST /api/los                          # Tạo LO mới
PUT /api/los/:id                       # Cập nhật LO
DELETE /api/los/:id                    # Xóa LO
POST /api/los/bulk/create              # Tạo hàng loạt LO
PUT /api/los/bulk/update               # Cập nhật hàng loạt LO
DELETE /api/los/bulk/delete            # Xóa hàng loạt LO
```

#### Statistics Endpoints (Admin + Teacher)
```http
GET /api/los/statistics/overview       # Thống kê tổng quan LO
GET /api/los/statistics/performance    # Phân tích hiệu suất LO
GET /api/los/:id/questions/statistics  # Thống kê câu hỏi theo LO
```

---

### ❓ Question Management
**Base URL**: `/api/questions`

#### Public Endpoints
```http
GET /api/questions                     # Lấy danh sách câu hỏi
GET /api/questions/:id                 # Lấy chi tiết câu hỏi
GET /api/questions/bylos               # Lấy câu hỏi theo LO
```

#### Admin & Teacher Endpoints
```http
POST /api/questions                    # Tạo câu hỏi mới
PUT /api/questions/:id                 # Cập nhật câu hỏi
DELETE /api/questions/:id              # Xóa câu hỏi
POST /api/questions/import             # Import câu hỏi từ CSV
POST /api/questions/import-excel       # Import câu hỏi từ Excel
```

---

### 💡 Answer Management
**Base URL**: `/api/answers`

#### Public Endpoints
```http
GET /api/answers                       # Lấy danh sách đáp án
GET /api/answers/:id                   # Lấy chi tiết đáp án
```

#### Admin & Teacher Endpoints
```http
POST /api/answers                      # Tạo đáp án mới
PUT /api/answers/:id                   # Cập nhật đáp án
DELETE /api/answers/:id                # Xóa đáp án
```

---

### 🎮 Quiz Management
**Base URL**: `/api/quizzes`

#### Public Endpoints
```http
GET /api/quizzes                       # Lấy danh sách quiz
GET /api/quizzes/:id                   # Lấy chi tiết quiz
GET /api/quizzes/:id/questions         # Lấy câu hỏi của quiz
GET /api/quizzes/:id/leaderboard       # Lấy bảng xếp hạng
GET /api/quizzes/pin/:pin              # Lấy quiz theo mã PIN
GET /api/quizzes/:id/participants      # Lấy danh sách người tham gia
```

#### Teacher & Admin Endpoints
```http
POST /api/quizzes                      # Tạo quiz mới
PUT /api/quizzes/:id                   # Cập nhật quiz
DELETE /api/quizzes/:id                # Xóa quiz
POST /api/quizzes/:id/start            # Bắt đầu quiz
POST /api/quizzes/:id/leaderboard      # Hiển thị bảng xếp hạng
POST /api/quizzes/:id/shuffle          # Xáo trộn câu hỏi
GET /api/quizzes/:id/realtime-scores   # Lấy điểm số realtime
GET /api/quizzes/:id/statistics        # Thống kê quiz
GET /api/quizzes/:quizId/student/:userId/score-history    # Lịch sử điểm sinh viên
GET /api/quizzes/:quizId/student/:userId/realtime         # Dữ liệu realtime sinh viên
GET /api/quizzes/teacher/:user_id/quizzes                 # Quiz của giảng viên
GET /api/quizzes/:id/analytics         # Phân tích quiz
GET /api/quizzes/:quizId/participants/:userId             # Chi tiết người tham gia
```

#### Student Endpoints
```http
POST /api/quizzes/:id/submit           # Nộp bài quiz
POST /api/quizzes/:id/join             # Tham gia quiz
GET /api/quizzes/:id/current-question  # Lấy câu hỏi hiện tại
GET /api/quizzes/:id/my-result         # Lấy kết quả cá nhân
POST /api/quizzes/:id/leave            # Rời khỏi quiz
POST /api/quizzes/realtime/answer      # Gửi đáp án realtime
```

---

### 📊 Quiz Results Management
**Base URL**: `/api/quiz-results`

#### Admin & Teacher Endpoints
```http
GET /api/quiz-results                  # Lấy tất cả kết quả quiz
POST /api/quiz-results                 # Tạo kết quả quiz
PUT /api/quiz-results/:id              # Cập nhật kết quả quiz
DELETE /api/quiz-results/:id           # Xóa kết quả quiz
GET /api/quiz-results/quiz/:quiz_id    # Kết quả theo quiz
GET /api/quiz-results/:quizId/radar/all # Tất cả dữ liệu radar
```

#### All Authenticated Users
```http
GET /api/quiz-results/:id              # Lấy kết quả theo ID
GET /api/quiz-results/quiz/:quizId/radar/average        # Dữ liệu radar trung bình
GET /api/quiz-results/quiz/:quizId/radar/top-performer  # Dữ liệu radar người xuất sắc
```

#### Student Endpoints
```http
GET /api/quiz-results/user/:user_id    # Kết quả của sinh viên
GET /api/quiz-results/quiz/:quizId/radar/current-user   # Dữ liệu radar cá nhân
```

---

### 🔗 Quiz-Question Relationships
**Base URL**: `/api/quiz-questions`

#### Public Endpoints
```http
GET /api/quiz-questions                # Lấy tất cả quan hệ quiz-question
GET /api/quiz-questions/:quiz_id/:question_id  # Lấy quan hệ cụ thể
```

#### Admin & Teacher Endpoints
```http
POST /api/quiz-questions               # Tạo quan hệ quiz-question
DELETE /api/quiz-questions/:quiz_id/:question_id  # Xóa quan hệ
```

---

### 🎓 Student Course Enrollment
**Base URL**: `/api/student-courses`

#### Public Endpoints
```http
GET /api/student-courses               # Lấy tất cả đăng ký môn học
GET /api/student-courses/:user_id/:course_id  # Lấy đăng ký cụ thể
```

#### Admin & Teacher Endpoints
```http
POST /api/student-courses              # Tạo đăng ký môn học
DELETE /api/student-courses/:user_id/:course_id  # Xóa đăng ký
```

---

### 📈 Course Results Management
**Base URL**: `/api/course-results`

#### Public Endpoints
```http
GET /api/course-results                # Lấy tất cả kết quả khóa học
GET /api/course-results/:id            # Lấy kết quả theo ID
```

#### Admin & Teacher Endpoints
```http
POST /api/course-results               # Tạo kết quả khóa học
PUT /api/course-results/:id            # Cập nhật kết quả
DELETE /api/course-results/:id         # Xóa kết quả
```

---

### 🏷️ Classification & Categorization APIs

#### Roles Management (`/api/roles`)
```http
GET /api/roles                         # Lấy danh sách vai trò
GET /api/roles/:id                     # Lấy chi tiết vai trò
POST /api/roles                        # Tạo vai trò mới
PUT /api/roles/:id                     # Cập nhật vai trò
DELETE /api/roles/:id                  # Xóa vai trò
```

#### Question Types (`/api/question-types`)
```http
GET /api/question-types                # Lấy danh sách loại câu hỏi
GET /api/question-types/:id            # Lấy chi tiết loại câu hỏi
POST /api/question-types               # Tạo loại câu hỏi mới
PUT /api/question-types/:id            # Cập nhật loại câu hỏi
DELETE /api/question-types/:id         # Xóa loại câu hỏi
```

#### Difficulty Levels (`/api/levels`)
```http
GET /api/levels                        # Lấy danh sách mức độ khó
GET /api/levels/:id                    # Lấy chi tiết mức độ
POST /api/levels                       # Tạo mức độ mới (Admin only)
PUT /api/levels/:id                    # Cập nhật mức độ (Admin only)
DELETE /api/levels/:id                 # Xóa mức độ (Admin only)
```

#### Subject Types (`/api/type-subjects`)
```http
GET /api/type-subjects                 # Lấy danh sách loại môn học
GET /api/type-subjects/:id             # Lấy chi tiết loại môn học
POST /api/type-subjects                # Tạo loại môn học mới
PUT /api/type-subjects/:id             # Cập nhật loại môn học
DELETE /api/type-subjects/:id          # Xóa loại môn học
```

#### Knowledge Types (`/api/type-of-knowledges`)
```http
GET /api/type-of-knowledges            # Lấy danh sách loại kiến thức
GET /api/type-of-knowledges/:id        # Lấy chi tiết loại kiến thức
POST /api/type-of-knowledges           # Tạo loại kiến thức mới
PUT /api/type-of-knowledges/:id        # Cập nhật loại kiến thức
DELETE /api/type-of-knowledges/:id     # Xóa loại kiến thức
```

#### Groups (`/api/groups`)
```http
GET /api/groups                        # Lấy danh sách nhóm
GET /api/groups/:id                    # Lấy chi tiết nhóm
POST /api/groups                       # Tạo nhóm mới
PUT /api/groups/:id                    # Cập nhật nhóm
DELETE /api/groups/:id                 # Xóa nhóm
```

---

### 🔗 Prerequisites Management
**Base URL**: `/api/tienquyets`

#### Public Endpoints
```http
GET /api/tienquyets                    # Lấy tất cả điều kiện tiên quyết
GET /api/tienquyets/:subject_id/:prerequisite_subject_id  # Lấy điều kiện cụ thể
```

#### Admin Only Endpoints
```http
POST /api/tienquyets                   # Tạo điều kiện tiên quyết
DELETE /api/tienquyets/:subject_id/:prerequisite_subject_id  # Xóa điều kiện
```

---

### 📊 Learning Analytics
**Base URL**: `/api/learning-analytics`

#### Admin & Teacher Endpoints
```http
POST /api/learning-analytics/program-analysis  # Tạo phân tích chương trình
GET /api/learning-analytics/program/:program_id/students  # Sinh viên theo tiến độ
```

---

### 📋 Reports & Statistics
**Base URL**: `/api/reports`

#### Admin & Teacher Endpoints
```http
GET /api/reports/program/:program_id/overview  # Báo cáo tổng quan chương trình
GET /api/reports/student/:user_id/program/:program_id/detail  # Báo cáo chi tiết sinh viên
GET /api/reports/program/:program_id/subjects/comparison     # Báo cáo so sánh môn học
```

---

### 📈 Advanced Statistics
**Base URL**: `/api/statistics`

#### Admin Only Endpoints
```http
GET /api/statistics/dashboard          # Dashboard tổng quan cho admin
```

#### Admin & Teacher Endpoints
```http
GET /api/statistics/comparative        # Thống kê so sánh
GET /api/statistics/trends             # Phân tích xu hướng
GET /api/statistics/performance/detailed  # Báo cáo hiệu suất chi tiết
```

---

### 🧪 Testing & Development
**Base URL**: `/api`

#### Development Endpoints
```http
GET /api/test-socket                   # Test Socket.IO connection
POST /api/quizzes/:id/test-leaderboard # Test leaderboard (development only)
```

---

### 🔄 Real-time Socket.IO Events

Hệ thống sử dụng Socket.IO để cung cấp các tính năng realtime cho quiz:

#### Quiz Events
```javascript
// Client Events (Gửi từ client)
'joinQuizRoom'          // Tham gia phòng quiz
'leaveQuizRoom'         // Rời khỏi phòng quiz
'submitAnswer'          // Gửi đáp án
'requestLeaderboard'    // Yêu cầu bảng xếp hạng

// Server Events (Nhận từ server)
'quizStarted'           // Quiz bắt đầu
'newQuestion'           // Câu hỏi mới
'showAnswerResult'      // Hiển thị kết quả đáp án
'nextQuestion'          // Chuyển câu hỏi tiếp theo
'quizCompleted'         // Quiz hoàn thành
'leaderboardUpdate'     // Cập nhật bảng xếp hạng
'newParticipant'        // Người tham gia mới
'participantLeft'       // Người tham gia rời đi
'connectionStatus'      // Trạng thái kết nối
```

---

### 📝 API Response Format

Tất cả API endpoints đều trả về dữ liệu theo format JSON chuẩn:

#### Success Response
```json
{
  "success": true,
  "data": {
    // Dữ liệu trả về
  },
  "message": "Thông báo thành công",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Thông báo lỗi chi tiết",
    "details": {
      // Chi tiết lỗi (nếu có)
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Pagination Response
```json
{
  "success": true,
  "data": {
    "items": [
      // Danh sách dữ liệu
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

### 🔐 Authentication Headers

Các API cần xác thực yêu cầu header Authorization:

```http
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### 📊 Rate Limiting

Hệ thống áp dụng rate limiting để bảo vệ API:
- **Public endpoints**: 100 requests/minute
- **Authenticated endpoints**: 1000 requests/minute
- **Admin endpoints**: 2000 requests/minute

## 🔧 Công nghệ sử dụng

### Frontend Dependencies
```json
{
  "next": "15.3.0",
  "react": "^19.0.0",
  "typescript": "^5",
  "tailwindcss": "^4",
  "socket.io-client": "^4.8.1",
  "@radix-ui/react-*": "Latest",
  "axios": "^1.8.4"
}
```

### Backend Dependencies
```json
{
  "express": "^5.1.0",
  "socket.io": "^4.8.1",
  "sequelize": "^6.37.7",
  "pg": "^8.14.1",
  "redis": "^4.7.0",
  "jsonwebtoken": "^9.0.2",
  "bcrypt": "^5.1.1",
  "firebase-admin": "^13.2.0"
}
```

## 📈 Tính năng nâng cao

### Real-time Features
- Live participant count
- Live leaderboard updates
- Question timer synchronization
- Connection status monitoring
- Auto-reconnection handling

### Analytics & Reporting
- User performance tracking
- Question difficulty analysis
- Learning outcome assessment
- Progress visualization
- Export functionality (Excel, CSV)

### Security Features
- Role-based access control
- JWT token authentication
- Password encryption
- Session management
- CORS protection
- Rate limiting

## 🎨 UI/UX Features

### Modern Interface
- Dark/Light theme support
- Responsive design
- Loading states
- Error handling
- Toast notifications
- Modal dialogs

### Accessibility
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus management

## 📝 Kết luận

Hệ thống QL_CTDT là một giải pháp toàn diện cho việc quản lý chương trình đào tạo và tổ chức thi trực tuyến. Với kiến trúc hiện đại, tính năng realtime mạnh mẽ và bộ API RESTful phong phú, hệ thống phù hợp cho các trường đại học, cao đẳng muốn số hóa quy trình giáo dục.

### Ưu điểm chính:
- ✅ **Kiến trúc microservices** dễ mở rộng và bảo trì
- ✅ **Tính năng realtime** mượt mà với Socket.IO
- ✅ **Bảo mật cao** với JWT và role-based access control
- ✅ **UI/UX hiện đại** và responsive design
- ✅ **Analytics và reporting** chi tiết và đa dạng
- ✅ **API RESTful** toàn diện với 100+ endpoints
- ✅ **Phân quyền chi tiết** theo vai trò người dùng
- ✅ **Deployment đơn giản** với Docker và Docker Compose
- ✅ **Tích hợp đa dạng** với PostgreSQL, Redis, Firebase
- ✅ **Hỗ trợ import/export** dữ liệu từ Excel/CSV

### Thống kê API:
- **16 nhóm API chính** với phân quyền chi tiết
- **100+ endpoints** phục vụ đầy đủ chức năng
- **4 mức phân quyền**: Public, Student, Teacher, Admin
- **Real-time events** cho quiz tương tác
- **Chuẩn RESTful** với response format nhất quán
- **Rate limiting** và bảo mật API

### Phù hợp cho:
- 🏫 **Trường đại học, cao đẳng** muốn số hóa giáo dục
- 👨‍🏫 **Giảng viên** cần công cụ tạo và quản lý bài kiểm tra
- 👨‍🎓 **Sinh viên** tham gia học tập và thi trực tuyến
- 🏢 **Tổ chức đào tạo** cần hệ thống quản lý chương trình
- 📊 **Nhà quản lý** cần báo cáo và phân tích chi tiết

---
*Tài liệu được cập nhật với đầy đủ thông tin API endpoints từ phân tích mã nguồn hệ thống QL_CTDT*
*Phiên bản: 2.0 - Cập nhật: 2024*
